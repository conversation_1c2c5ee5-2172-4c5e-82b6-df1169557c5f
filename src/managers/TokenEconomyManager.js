import { GAME_CONFIG } from '../config/gameConfig.js';
import { DailyRewardTracker } from './DailyRewardTracker.js';

/**
 * TokenEconomyManager handles WISH token tracking, transactions, and rewards
 * Implements performance-based token calculation and balance management
 */
export class TokenEconomyManager {
    constructor() {
        // Initialize daily reward tracker - will be set when wallet connects
        this.dailyRewardTracker = null;
        
        // Token balance
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;

        // Transaction history
        this.transactionHistory = [];
        this.maxHistorySize = 100;

        // Performance tracking for rewards
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };

        // Load saved data from localStorage immediately
        this.loadFromStorage();
        
        // Token reward multipliers 
        this.rewardMultipliers = {
            base: 1.0,
            speed: 1.125,    // was 1.5, now 1.0 + (1.5 - 1.0) * 0.25 = 1.125
            accuracy: 1.075, // was 1.3, now 1.0 + (1.3 - 1.0) * 0.25 = 1.075
            perfect: 1.25,   // was 2.0, now 1.0 + (2.0 - 1.0) * 0.25 = 1.25
            difficulty: 1.0
        };
        
        // Visual feedback state
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        // Callbacks for UI updates
        this.onBalanceUpdateCallback = null;
        this.onTransactionCallback = null;
        this.onRewardEarnedCallback = null;
        
        // Debug mode
        this.debugMode = false;

        // Wallet integration state
        this.walletConnected = false;
        this.walletAddress = null;
        this.walletBalance = 0;
        this.walletProvider = null;
        
        // HOT WALLET CONFIGURATION - Address only (private key is now securely on server)
        this.hotWalletAddress = '******************************************';
        // this.hotWalletPrivateKey = '0xde9be858da4a475276426320d5e9262ecfc3ba460bfac56360bfa6c4c28b4ee0'; // REMOVED FOR SECURITY
        
        // API configuration
        this.apiBaseUrl = 'http://localhost:3001/api'; // Default server URL
    }
    
    /**
     * Helper method to make authenticated API calls to the server
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method (GET, POST, etc.)
     * @param {object} data - Request data (for POST/PUT requests)
     * @returns {Promise<object>} API response
     */
    async makeAuthenticatedApiCall(endpoint, method = 'GET', data = null) {
        try {
            const url = `${this.apiBaseUrl}${endpoint}`;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    // In a real implementation, this would be a proper auth token
                    'Authorization': 'Bearer secure-token-for-development'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(url, options);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API call to ${endpoint} failed:`, error);
            throw error;
        }
    }
     
    /**
     * Helper method to make authenticated API calls to the server
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method (GET, POST, etc.)
     * @param {object} data - Request data (for POST/PUT requests)
     * @returns {Promise<object>} API response
     */
    async makeAuthenticatedApiCall(endpoint, method = 'GET', data = null) {
        try {
            const url = `${this.apiBaseUrl}${endpoint}`;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    // In a real implementation, this would be a proper auth token
                    'Authorization': 'Bearer secure-token-for-development'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(url, options);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API call to ${endpoint} failed:`, error);
            throw error;
        }
    }
        
    /**
     * Initialize wallet connection with OrangeID integration
     * @private
     */
    initializeWallet() {
        console.log('🔧 Initializing wallet connection...');
        console.log('🔧 Current wallet state before init:', {
            connected: this.walletConnected,
            address: this.walletAddress,
            provider: this.walletProvider
        });
        
        // First check if user is logged in with OrangeID and has wallet address
        if (this.checkOrangeIDWallet()) {
            console.log('✅ Using OrangeID wallet address');
            console.log('🔧 Wallet state after OrangeID init:', {
                connected: this.walletConnected,
                address: this.walletAddress,
                provider: this.walletProvider
            });
            return;
        }

        // Check for wallet providers in order of preference (only in browser environment)
        if (typeof window !== 'undefined' && typeof window.ethereum !== 'undefined') {
            console.log('✅ MetaMask/Wallet detected (window.ethereum)');
            this.walletProvider = window.ethereum;
            this.setupWalletEventListeners();

            // Check if already connected
            if (window.ethereum.selectedAddress) {
                console.log('✅ Already connected to wallet:', window.ethereum.selectedAddress);
                this.walletAddress = window.ethereum.selectedAddress;
                this.walletConnected = true;
                this.syncWalletBalance();
                this.initializeDailyRewardTracker();
            }
        } else if (typeof window !== 'undefined' && typeof window.web3 !== 'undefined') {
            console.log('✅ Legacy web3 provider detected');
            this.walletProvider = window.web3.currentProvider;
        } else if (this.debugMode) {
            console.log('🐛 Debug mode: Using localStorage wallet simulation');
            this.walletConnected = true;
            this.walletAddress = '0xDebugWalletAddress';
            this.syncWalletBalance();
        } else {
            console.warn('⚠️ No cryptocurrency wallet detected');
        }
        
        console.log('🔧 Wallet initialization complete:', {
            connected: this.walletConnected,
            address: this.walletAddress,
            provider: this.walletProvider ? 'detected' : 'none'
        });
    }

    /**
     * Check if user has OrangeID wallet address
     * @returns {boolean} True if OrangeID wallet is available
     * @private
     */
    checkOrangeIDWallet() {
        console.log('🔍 Checking for OrangeID wallet...');
        
        // Check if AuthManager is available and user is authenticated with wallet
        if (typeof window !== 'undefined' && window.AuthManager && window.AuthManager.isAuthenticated) {
            console.log('🔍 Found global AuthManager');
            const user = window.AuthManager.getUser();
            console.log('👤 Global AuthManager user:', user);
            if (user && user.ethAddress) {
                console.log('✅ Found wallet address in global AuthManager:', user.ethAddress);
                this.walletConnected = true;
                this.walletAddress = user.ethAddress;
                this.walletProvider = 'orangeid'; // Mark as OrangeID provider
                this.syncWalletBalance();
                return true;
            } else {
                console.log('⚠️ Global AuthManager user missing ethAddress');
            }
        } else {
            console.log('⚠️ Global AuthManager not available or not authenticated');
        }
        
        // Also check through game engine's auth manager
        if (this.gameEngine &&
            this.gameEngine.mainMenu &&
            this.gameEngine.mainMenu.authManager &&
            this.gameEngine.mainMenu.authManager.isAuthenticated) {
            console.log('🔍 Found game engine AuthManager');
            const user = this.gameEngine.mainMenu.authManager.getUser();
            console.log('👤 Game engine AuthManager user:', user);
            if (user && user.ethAddress) {
                console.log('✅ Found wallet address in game engine AuthManager:', user.ethAddress);
                this.walletConnected = true;
                this.walletAddress = user.ethAddress;
                this.walletProvider = 'orangeid'; // Mark as OrangeID provider
                this.syncWalletBalance();
                return true;
            } else {
                console.log('⚠️ Game engine AuthManager user missing ethAddress');
            }
        } else {
            console.log('⚠️ Game engine AuthManager not available or not authenticated');
        }
        
        console.log('❌ No OrangeID wallet found');
        return false;
    }

    /**
     * Initialize daily reward tracker for the current user
     * @private
     */
    initializeDailyRewardTracker() {
        if (this.walletAddress) {
            this.dailyRewardTracker = new DailyRewardTracker(this.walletAddress);
            console.log(`✅ Initialized daily reward tracker for user: ${this.walletAddress}`);
        }
    }

    /**
     * Setup wallet event listeners
     * @private
     */
    setupWalletEventListeners() {
        if (!this.walletProvider || !this.walletProvider.on) return;

        // INTERCEPT WALLET REQUESTS TO PREVENT CHAIN SWITCHING TO MAINNET
        const originalRequest = this.walletProvider.request;
        this.walletProvider.request = async (args) => {
            // Block any attempts to switch to mainnet (chain ID 1)
            if (args.method === 'wallet_switchEthereumChain' || args.method === 'wallet_addEthereumChain') {
                const params = args.params[0];
                if (params && params.chainId) {
                    const requestedChainId = parseInt(params.chainId, 16);
                    if (requestedChainId === 1) {
                        console.log('🚨 BLOCKED ATTEMPT TO SWITCH TO MAINNET - FORCING CHAIN 31337');
                        // Force switch to Chain ID 31337 instead
                        return originalRequest.call(this.walletProvider, {
                            method: 'wallet_switchEthereumChain',
                            params: [{ chainId: '0x7a69' }] // 31337 in hex
                        });
                    }
                }
            }
            return originalRequest.call(this.walletProvider, args);
        };

        // Listen for account changes
        this.walletProvider.on('accountsChanged', (accounts) => {
            if (accounts.length === 0) {
                this.handleWalletDisconnect();
            } else {
                this.handleAccountsChanged(accounts);
            }
        });

        // Listen for chain changes
        this.walletProvider.on('chainChanged', (chainId) => {
            this.handleChainChanged(chainId);
        });

        // Listen for disconnect
        this.walletProvider.on('disconnect', (error) => {
            this.handleWalletDisconnect(error);
        });
    }

    /**
     * Handle accounts changed event
     * @param {Array} accounts - New account array
     * @private
     */
    handleAccountsChanged(accounts) {
        if (accounts.length > 0) {
            this.walletAddress = accounts[0];
            this.walletConnected = true;
            this.syncWalletBalance();
            this.triggerWalletUpdate();
        } else {
            this.handleWalletDisconnect();
        }
    }

    /**
     * Handle chain changed event
     * @param {string} chainId - New chain ID
     * @private
     */
    handleChainChanged(chainId) {
        console.log('Chain changed to:', chainId);
        
        // FORCE STAY ON CHAIN ID 31337 - PREVENT ORANGEID FROM SWITCHING TO MAINNET
        const targetChainId = '0x7a69'; // 31337 in hex
        const currentChainDecimal = parseInt(chainId, 16);
        
        if (currentChainDecimal === 1) {
            console.log('🚨 ORANGEID TRYING TO SWITCH TO MAINNET - FORCING BACK TO CHAIN 31337!');
            
            // Immediately switch back to Chain ID 31337
            if (this.walletProvider && this.walletProvider.request) {
                this.walletProvider.request({
                    method: 'wallet_switchEthereumChain',
                    params: [{ chainId: targetChainId }]
                }).then(() => {
                    console.log('✅ FORCED BACK TO CHAIN 31337 - ORANGEID BLOCKED');
                }).catch((error) => {
                    console.error('❌ Failed to force back to Chain 31337:', error);
                });
            }
            
            // Don't proceed with balance sync on mainnet
            return;
        }
        
        // Re-sync balance on chain change only if we're on the correct chain
        if (this.walletConnected && currentChainDecimal === 31337) {
            this.syncWalletBalance();
        }
    }

    /**
     * Handle wallet disconnect
     * @param {Error} error - Disconnect error if any
     * @private
     */
    handleWalletDisconnect(error = null) {
        this.walletConnected = false;
        this.walletAddress = null;
        this.walletBalance = 0;
        console.log('Wallet disconnected', error);
        this.triggerWalletUpdate();
    }

    /**
     * Connect to cryptocurrency wallet - ENSURE CONNECTION TO CHAIN ID 31337
     * @returns {Promise<object>} Connection result
     */
    async connectWallet() {
        if (this.debugMode) {
            // In debug mode, simulate wallet connection
            this.walletConnected = true;
            this.walletAddress = '0xDebugWalletAddress';
            await this.syncWalletBalance();
            return {
                success: true,
                address: this.walletAddress,
                balance: this.walletBalance,
                isDebug: true
            };
        }

        if (!this.walletProvider) {
            return {
                success: false,
                error: 'No wallet provider available',
                message: 'Please install a cryptocurrency wallet like MetaMask'
            };
        }

        try {
            console.log('🔌 ENSURING CONNECTION TO CHAIN ID 31337...');
            
            // FORCE CONNECTION TO CHAIN ID 31337 (Hardhat local development)
            const targetChainId = '0x7a69'; // 31337 in hex
            console.log('🎯 Target Chain ID for ETH Test Mode:', targetChainId);
            
            try {
                // Check current chain
                const currentChainId = await this.walletProvider.request({ method: 'eth_chainId' });
                console.log('🔗 Current Chain ID:', currentChainId);
                
                if (currentChainId !== targetChainId) {
                    console.log('🔄 Switching to Chain ID 31337 for ETH Test Mode...');
                    
                    // Request chain switch to 31337
                    await this.walletProvider.request({
                        method: 'wallet_switchEthereumChain',
                        params: [{ chainId: targetChainId }]
                    });
                    
                    console.log('✅ Successfully switched to Chain ID 31337');
                } else {
                    console.log('✅ Already connected to Chain ID 31337');
                }
            } catch (switchError) {
                console.error('❌ Failed to switch to Chain ID 31337:', switchError);
                
                // If switching fails, try to add the network first
                if (switchError.code === 4902) {
                    console.log('📝 Chain ID 31337 not in MetaMask, adding network...');
                    
                    try {
                        await this.walletProvider.request({
                            method: 'wallet_addEthereumChain',
                            params: [{
                                chainId: targetChainId,
                                chainName: 'Local Hardhat Development',
                                rpcUrls: ['http://localhost:8545'],
                                nativeCurrency: {
                                    name: 'ETH',
                                    symbol: 'ETH',
                                    decimals: 18
                                }
                            }]
                        });
                        
                        console.log('✅ Added Chain ID 31337 to MetaMask');
                        
                        // Now switch to it
                        await this.walletProvider.request({
                            method: 'wallet_switchEthereumChain',
                            params: [{ chainId: targetChainId }]
                        });
                        
                        console.log('✅ Successfully switched to Chain ID 31337');
                    } catch (addError) {
                        console.error('❌ Failed to add Chain ID 31337:', addError);
                        throw new Error('Could not add/switch to Chain ID 31337. Please manually add the local development network to MetaMask.');
                    }
                } else {
                    throw switchError;
                }
            }
            
            // Request account access
            const accounts = await this.walletProvider.request({
                method: 'eth_requestAccounts'
            });

            if (accounts && accounts.length > 0) {
                this.walletAddress = accounts[0];
                this.walletConnected = true;
                
                console.log('✅ Wallet connected to Chain ID 31337:', this.walletAddress);
                
                // Sync wallet balance
                await this.syncWalletBalance();
                this.triggerWalletUpdate();
                
                return {
                    success: true,
                    address: this.walletAddress,
                    balance: this.walletBalance,
                    chainId: targetChainId
                };
            } else {
                return {
                    success: false,
                    error: 'No accounts found',
                    message: 'Please unlock your wallet and try again'
                };
            }
        } catch (error) {
            console.error('❌ Wallet connection to Chain ID 31337 failed:', error);
            console.error('Error details:', error.message, error.code);
            
            return {
                success: false,
                error: error.message,
                message: 'Failed to connect wallet to Chain ID 31337 for ETH Test Mode',
                code: error.code
            };
        }
    }

    /**
     * Sync wallet balance from blockchain, OrangeID, or debug storage
     * @returns {Promise<number>} Current wallet balance
     */
    async syncWalletBalance() {
        if (this.debugMode) {
            // In debug mode, use localStorage for wallet balance (browser only)
            if (typeof localStorage !== 'undefined') {
                const debugBalance = localStorage.getItem('debugWalletBalance');
                this.walletBalance = debugBalance ? parseInt(debugBalance, 10) : 1000; // Default 1000 in debug
                console.log(`🐛 Debug mode: Using balance ${this.walletBalance} from localStorage`);
            } else {
                this.walletBalance = 1000; // Default for Node.js
                console.log(`🐛 Debug mode (Node.js): Using default balance ${this.walletBalance}`);
            }
            return this.walletBalance;
        }

        if (!this.walletConnected) {
            console.log('⚠️ Wallet not connected, returning 0 balance');
            this.walletBalance = 0;
            return 0;
        }

        try {
            if (this.walletProvider === 'orangeid') {
                // Use OrangeID API to get wallet balance
                console.log('📱 Using OrangeID for balance sync');
                return await this.syncOrangeIDWalletBalance();
            } else if (this.walletProvider && typeof this.walletProvider.request === 'function') {
                // Standard Ethereum wallet (MetaMask, etc.)
                console.log('🔍 Requesting balance from wallet provider for address:', this.walletAddress);
                console.log('🔗 Provider type:', typeof this.walletProvider);
                console.log('🔗 Provider has request method:', typeof this.walletProvider.request);
                
                // Get the current network info for debugging - ENSURE WE USE CHAIN ID 31337
                try {
                    const chainId = await this.walletProvider.request({ method: 'eth_chainId' });
                    console.log('🔗 Current chain ID:', chainId);
                    console.log('🔗 Chain ID in decimal:', parseInt(chainId, 16));
                    
                    const blockNumber = await this.walletProvider.request({ method: 'eth_blockNumber' });
                    console.log('🔗 Current block number:', blockNumber);
                    console.log('🔗 Block number in decimal:', parseInt(blockNumber, 16));
                    
                    // ENSURE WE DETECT CHAIN ID 31337 FOR LOCAL DEVELOPMENT
                    const chainIdDecimal = parseInt(chainId, 16);
                    console.log(`🔍 CHECKING CHAIN ID: ${chainIdDecimal} (expected: 31337 for Hardhat)`);
                    
                    if (chainIdDecimal === 31337) {
                        console.log('✅ CORRECT: Connected to Hardhat local development network (Chain ID: 31337)');
                    } else if (chainIdDecimal === 1337) {
                        console.log('✅ CORRECT: Connected to Ganache local development network (Chain ID: 1337)');
                    } else if (chainIdDecimal === 1) {
                        console.warn('❌ WRONG: Connected to Ethereum Mainnet (Chain ID: 1) - SHOULD BE 31337!');
                        console.warn('❌ ETH Test Mode requires Chain ID 31337 for local development');
                    } else {
                        console.log(`⚠️ UNEXPECTED: Connected to network with Chain ID: ${chainIdDecimal} (expected 31337)`);
                    }
                } catch (networkError) {
                    console.warn('⚠️ Could not get network info:', networkError);
                }
                
                const balance = await this.walletProvider.request({
                    method: 'eth_getBalance',
                    params: [this.walletAddress, 'latest']
                });

                console.log('📊 Raw balance from provider (wei):', balance);
                console.log('📊 Balance type:', typeof balance);

                // Convert from wei to ETH (18 decimal places)
                let balanceInEth;
                try {
                    if (typeof balance === 'string' && balance.startsWith('0x')) {
                        // Hex string from Ethereum
                        const balanceInWei = BigInt(balance);
                        balanceInEth = Number(balanceInWei) / 1e18;
                    } else if (typeof balance === 'bigint') {
                        // Already a BigInt
                        balanceInEth = Number(balance) / 1e18;
                    } else if (typeof balance === 'string') {
                        // Decimal string
                        balanceInEth = parseFloat(balance) / 1e18;
                    } else {
                        // Fallback - assume it's already in ETH
                        balanceInEth = parseFloat(balance);
                    }
                    
                    this.walletBalance = balanceInEth;
                    
                    console.log(`✅ Wallet balance synced: ${this.walletBalance} ETH for address: ${this.walletAddress}`);
                    console.log(`✅ This equals ${balanceInEth} ETH from raw balance: ${balance}`);
                    
                } catch (conversionError) {
                    console.error('❌ Failed to convert balance:', conversionError);
                    console.log('🔄 Using raw balance as fallback:', balance);
                    this.walletBalance = parseFloat(balance) || 0;
                }
                
                return this.walletBalance;
            } else {
                console.warn('⚠️ Unsupported wallet provider:', this.walletProvider);
                this.walletBalance = 0;
                return 0;
            }
        } catch (error) {
            console.error('❌ Failed to sync wallet balance:', error);
            console.error('Error details:', error.message, error.code, error.stack);
            
            // For local development, try to get some basic info even if balance fails
            console.log('🔧 Attempting fallback balance check for local development...');
            
            try {
                // Try to get chain ID as a basic connectivity test
                const chainId = await this.walletProvider.request({ method: 'eth_chainId' });
                console.log('🔗 Chain ID available:', chainId);
                console.log('🔗 Chain ID in decimal:', parseInt(chainId, 16));
                
                // Check for local development networks
                const chainIdDecimal = parseInt(chainId, 16);
                console.log(`🔍 Checking if chain ID ${chainIdDecimal} is a local development network`);
                
                // Hardhat: 31337 (0x7a69), Ganache: 1337 (0x539), Localhost: 1 (0x1) but usually different
                if (chainIdDecimal === 31337 || chainIdDecimal === 1337 || chainId === '0x7a69' || chainId === '0x539') {
                    console.log('🧪 Local development network detected!');
                    
                    // Try to get the actual balance one more time with different parameters
                    try {
                        console.log('🔄 Retrying balance check with different parameters...');
                        const balanceRetry = await this.walletProvider.request({
                            method: 'eth_getBalance',
                            params: [this.walletAddress, 'latest']
                        });
                        console.log('🔄 Retry balance result:', balanceRetry);
                        
                        if (balanceRetry && balanceRetry !== '0x0') {
                            // Found balance on retry
                            const balanceInWei = BigInt(balanceRetry);
                            const balanceInEth = Number(balanceInWei) / 1e18;
                            this.walletBalance = balanceInEth;
                            console.log(`✅ Retry successful: ${this.walletBalance} ETH`);
                            return this.walletBalance;
                        }
                    } catch (retryError) {
                        console.log('🔄 Retry failed, using test balance:', retryError);
                    }
                    
                    // If retry failed or balance is 0, use test balance for local development
                    console.log('🧪 Using test balance for local development');
                    this.walletBalance = 10000; // 10,000 ETH for testing
                    console.log(`🧪 Using test balance: ${this.walletBalance} ETH for local development`);
                    return this.walletBalance;
                } else {
                    console.log(`🔍 Chain ID ${chainIdDecimal} is not a recognized local development network`);
                }
            } catch (fallbackError) {
                console.error('❌ Fallback balance check also failed:', fallbackError);
            }
            
            this.walletBalance = 0;
            return 0;
        }
    }

    /**
     * Sync wallet balance from OrangeID API
     * @returns {Promise<number>} Current wallet balance from OrangeID
     * @private
     */
    async syncOrangeIDWalletBalance() {
        console.log('📱 Syncing OrangeID wallet balance...');
        console.log('📱 Current wallet state:', {
            address: this.walletAddress,
            connected: this.walletConnected,
            provider: this.walletProvider
        });
        
        if (this.debugMode) {
            // In debug mode, use localStorage for wallet balance (browser only)
            if (typeof localStorage !== 'undefined') {
                const debugBalance = localStorage.getItem('debugWalletBalance');
                this.walletBalance = debugBalance ? parseInt(debugBalance, 10) : 1000; // Default 1000 in debug
            } else {
                this.walletBalance = 1000; // Default for Node.js
            }
            console.log('🐛 Debug mode balance:', this.walletBalance);
            return this.walletBalance;
        }

        // Check if we have a valid OrangeID wallet connection
        if (!this.walletConnected || this.walletProvider !== 'orangeid' || !this.walletAddress) {
            console.warn('OrangeID wallet not properly connected for balance sync');
            console.log('Current wallet state:', {
                connected: this.walletConnected,
                provider: this.walletProvider,
                address: this.walletAddress
            });
            this.walletBalance = 0;
            return 0;
        }
        
        console.log('📱 Using existing OrangeID wallet for balance sync');

        try {
            // OrangeID provides the wallet address, but we still use MetaMask to get the actual ETH balance
            console.log('Syncing OrangeID wallet balance for address:', this.walletAddress);
            
            // Use MetaMask (window.ethereum) to get the actual ETH balance
            if (typeof window !== 'undefined' && window.ethereum && window.ethereum.request) {
                console.log('📱 Using OrangeID + MetaMask for balance sync');
                
                const balance = await window.ethereum.request({
                    method: 'eth_getBalance',
                    params: [this.walletAddress, 'latest']
                });

                console.log('📊 Raw balance from MetaMask (wei):', balance);

                // Convert from wei to ETH (18 decimal places)
                let balanceInEth;
                if (typeof balance === 'string') {
                    // Handle hex string from MetaMask
                    balanceInEth = parseInt(balance, 16) / 1e18;
                } else if (typeof balance === 'bigint') {
                    // Handle bigint
                    balanceInEth = Number(balance) / 1e18;
                } else {
                    // Handle number
                    balanceInEth = balance / 1e18;
                }

                console.log('💰 Balance in ETH:', balanceInEth);
                this.walletBalance = balanceInEth;
                return this.walletBalance;
            } else {
                console.warn('⚠️ MetaMask not available for balance sync with OrangeID');
                this.walletBalance = 0;
                return 0;
            }
            
        } catch (error) {
            console.error('Failed to sync OrangeID wallet balance:', error);
            this.walletBalance = 0;
            return 0;
        }
    }

    /**
     * Get current wallet balance (cached value)
     * @returns {number} Current wallet balance
     */
    getWalletBalance() {
        return this.walletBalance;
    }

    /**
     * Get formatted balance display string
     * @returns {string} Formatted balance display
     */
    getBalanceDisplay() {
        // Only show ETH balance if ETH test mode is actually enabled
        // Check if ethTestModeManager exists and is in test mode
        if (typeof window !== 'undefined' && window.gameEngine?.ethTestModeManager?.isTestMode) {
            return `${this.walletBalance.toFixed(4)} ETH (Test Mode)`;
        }
        // Otherwise show WISH token balance
        return `${this.playerBalance} WISH`;
    }

    /**
     * Check if wallet is connected
     * @returns {boolean} Wallet connection status
     */
    isWalletConnected() {
        return this.walletConnected;
    }

    /**
     * Get wallet address
     * @returns {string|null} Wallet address or null if not connected
     */
    getWalletAddress() {
        return this.walletAddress;
    }
    
    /**
     * Set wallet address
     * @param {string} address - Wallet address
     */
    setWalletAddress(address) {
        this.walletAddress = address;
    }

    /**
     * Trigger wallet update callback
     * @private
     */
    triggerWalletUpdate() {
        if (this.onWalletUpdateCallback) {
            this.onWalletUpdateCallback({
                connected: this.walletConnected,
                address: this.walletAddress,
                balance: this.walletBalance
            });
        }
    }

    /**
     * Set wallet update callback
     * @param {function} callback - Callback function
     */
    setOnWalletUpdate(callback) {
        this.onWalletUpdateCallback = callback;
    }

    /**
     * Calculate token reward based on level completion performance
     * @param {object} completionData - Level completion data from LevelManager
     * @returns {object} Token reward breakdown
     */
    /**
     * Calculate token reward based on level completion with new daily max system
     * @param {object} completionData - Level completion data from LevelManager
     * @param {object} levelConfig - Level configuration with enemy data
     * @returns {object} Token reward breakdown
     */
    calculateLevelReward(completionData, levelConfig = null) {
        if (!completionData.completed) {
            return { totalReward: 0, breakdown: { reason: 'level_not_completed' } };
        }
        
        const levelNumber = completionData.levelNumber;
        
        // Check if level can be rewarded today
        if (!this.dailyRewardTracker.canEarnReward(levelNumber)) {
            return { totalReward: 0, breakdown: { reason: 'level_already_completed_today' } };
        }
        
        // Check if daily limit has been reached
        if (this.dailyRewardTracker.isDailyLimitReached()) {
            return { totalReward: 0, breakdown: { reason: 'daily_limit_reached' } };
        }
        
        // Calculate reward based on completion percentage
        const levelData = {
            totalEnemies: levelConfig?.totalEnemies || completionData.totalEnemies || completionData.enemiesDefeated || 0,
            enemiesDefeated: completionData.enemiesDefeated || 0
        };
        
        let totalReward = this.dailyRewardTracker.calculateCompletionReward(levelNumber, levelData);
        
        // Apply daily capacity limit
        const remainingCapacity = this.dailyRewardTracker.getRemainingDailyCapacity();
        totalReward = Math.min(totalReward, remainingCapacity);
        
        // If no reward due to daily limits, return early
        if (totalReward <= 0) {
            return { totalReward: 0, breakdown: { reason: 'no_reward_due_to_limits' } };
        }
        
        const maxReward = this.dailyRewardTracker.calculateMaxReward(levelNumber);
        const completionPercentage = levelData.totalEnemies > 0 ?
            levelData.enemiesDefeated / levelData.totalEnemies : 0;
        
        const breakdown = {
            maxReward: maxReward,
            completionPercentage: completionPercentage,
            completionReward: totalReward,
            totalReward: totalReward,
            dailyLimitInfo: {
                dailyTokensEarned: this.dailyRewardTracker.getDailyProgress().dailyTokensEarned,
                remainingCapacity: remainingCapacity,
                isLimitReached: this.dailyRewardTracker.isDailyLimitReached()
            }
        };
        
        return { totalReward, breakdown };
    }
    
    /**
     * Calculate base reward for level completion
     * @param {number} levelNumber - Level number
     * @returns {number} Base reward amount
     */
    calculateBaseReward(levelNumber) {
        const baseAmount = GAME_CONFIG.BASE_LEVEL_REWARD;
        const levelMultiplier = Math.floor((levelNumber - 1) / 5) + 1; // Increases every 5 levels
        return baseAmount * levelMultiplier;
    }
    
    /**
     * Calculate time bonus (faster completion = more tokens)
     * @param {number} completionTime - Time taken in seconds
     * @param {number} levelNumber - Level number
     * @returns {number} Time bonus amount
     */
    calculateTimeBonus(completionTime, levelNumber) {
        // Expected completion time increases with level
        const expectedTime = 60 + (levelNumber * 10); // Base 60s + 10s per level
        const fastTime = expectedTime * 0.5; // 50% of expected time for max bonus
        
        if (completionTime <= fastTime) {
            // Excellent time - maximum bonus 
            return Math.floor(750 + (levelNumber * 125));
        } else if (completionTime <= expectedTime * 0.75) {
            // Good time - partial bonus 
            return Math.floor(500 + (levelNumber * 75));
        } else if (completionTime <= expectedTime) {
            // Acceptable time - small bonus 
            return Math.floor(250 + (levelNumber * 25));
        }
        
        return 0; // No bonus for slow completion
    }
    
    /**
     * Calculate score bonus based on points earned
     * @param {number} score - Score achieved
     * @param {number} levelNumber - Level number
     * @returns {number} Score bonus amount
     */
    calculateScoreBonus(score, levelNumber) {
        // Score bonus is a percentage of the score, scaled by level 
        const basePercentage = 0.25; // 25% of score as base (reduced from 100% by 75%)
        const levelScaling = Math.min(2.0, 1.0 + (levelNumber * 0.05)); // Up to 2x scaling

        return Math.floor(score * basePercentage * levelScaling);
    }
    
    /**
     * Calculate speed bonus for fast completion
     * @param {number} baseReward - Base reward amount
     * @returns {number} Speed bonus amount
     */
    calculateSpeedBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.speed - 1.0));
    }
    
    /**
     * Calculate accuracy bonus for high accuracy
     * @param {number} baseReward - Base reward amount
     * @returns {number} Accuracy bonus amount
     */
    calculateAccuracyBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.accuracy - 1.0));
    }
    
    /**
     * Calculate perfect completion bonus
     * @param {number} baseReward - Base reward amount
     * @returns {number} Perfect bonus amount
     */
    calculatePerfectBonus(baseReward) {
        return Math.floor(baseReward * (this.rewardMultipliers.perfect - 1.0));
    }
    
    /**
     * Calculate difficulty multiplier based on level
     * @param {number} levelNumber - Level number
     * @returns {number} Difficulty multiplier
     */
    calculateDifficultyMultiplier(levelNumber) {
        // Multiplier increases gradually with level
        const baseMultiplier = 1.0;
        const increment = 0.1;
        const maxMultiplier = 3.0;
        
        const multiplier = baseMultiplier + (Math.floor((levelNumber - 1) / 10) * increment);
        return Math.min(maxMultiplier, multiplier);
    }
    
    /**
     * Award tokens to player balance after verifying wallet and high score
     * @param {number} amount - Amount to award
     * @param {string} reason - Reason for the award
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     */
    /**
     * Award tokens to player balance with daily tracking for level completions
     * @param {number} amount - Amount to award
     * @param {string} reason - Reason for the award
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     */
    async awardTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }

        // For level completion, ensure we have the latest high score from OrangeSDK
        if (reason === 'level_completion' && typeof window !== 'undefined' && window.OrangeSDKManager) {
            try {
                const sessionHighScore = window.OrangeSDKManager.getCurrentSessionHighScore();
                const currentScore = metadata.score || 0;
                
                // Only award tokens if this score is a new high score for the session
                if (currentScore > sessionHighScore) {
                    metadata.sessionHighScore = sessionHighScore;
                    metadata.isNewHighScore = true;
                } else {
                    metadata.sessionHighScore = sessionHighScore;
                    metadata.isNewHighScore = false;
                    console.log('Score not high enough for token award. Current high:', sessionHighScore, 'This score:', currentScore);
                }
            } catch (error) {
                console.warn('Failed to get session high score from OrangeSDK:', error);
            }
        }
        
        // For level completion, update daily tracking
        if (reason === 'level_completion' && metadata.levelNumber) {
            const levelNumber = metadata.levelNumber;
            const completionPercentage = metadata.completionPercentage || 1.0;
            
            // Record in daily tracker
            this.dailyRewardTracker.recordLevelCompletion(levelNumber, completionPercentage, amount);
        }
        
        // Check if we're in ETH test mode - support both browser and Node.js environments
        const isEthTestMode = (typeof window !== 'undefined') ?
            window.gameEngine?.ethTestModeManager?.isTestMode :
            (this.ethTestModeManager?.isTestMode || false);
        
        if (isEthTestMode) {
            // In ETH test mode, handle different reward types
            console.log(`🧪 ETH Test Mode: Processing ${reason} reward of ${amount} ETH`);

            if (reason === 'level_completion') {
                // Level completion rewards: Hot wallet sends ETH to player
                const recipientAddress = this.walletAddress;
                console.log(`🧪 ETH Test Mode: Level completion reward - sending ${amount} ETH from hot wallet to player ${recipientAddress}`);

                // Call the server API to send ETH from hot wallet to player with retry logic
                let retryCount = 0;
                const maxRetries = 3;
                let lastError;

                while (retryCount <= maxRetries) {
                    try {
                        const response = await fetch('http://localhost:3001/api/wallet/send', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer secure-token-for-development' // Development auth token
                            },
                            body: JSON.stringify({
                                toAddress: recipientAddress,
                                amount: amount.toString(),
                                reason: `Level completion reward for level ${metadata?.levelNumber || 'unknown'}`
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            console.log('✅ ETH Test Mode: Hot wallet transaction successful:', result.transactionHash);

                            return {
                                success: true,
                                transaction: {
                                    amount: amount,
                                    reason: reason,
                                    transactionHash: result.transactionHash,
                                    timestamp: Date.now()
                                },
                                newBalance: this.playerBalance + amount
                            };
                        } else if (response.status === 429 && retryCount < maxRetries) {
                            // Rate limited - wait and retry
                            const waitTime = Math.pow(2, retryCount) * 1000; // Exponential backoff: 1s, 2s, 4s
                            console.log(`⏳ ETH Test Mode: Rate limited (429), retrying in ${waitTime}ms (attempt ${retryCount + 1}/${maxRetries + 1})`);
                            await new Promise(resolve => setTimeout(resolve, waitTime));
                            retryCount++;
                            continue;
                        } else {
                            throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
                        }
                    } catch (error) {
                        lastError = error;
                        if (retryCount < maxRetries) {
                            const waitTime = Math.pow(2, retryCount) * 1000;
                            console.log(`⏳ ETH Test Mode: Request failed, retrying in ${waitTime}ms (attempt ${retryCount + 1}/${maxRetries + 1}): ${error.message}`);
                            await new Promise(resolve => setTimeout(resolve, waitTime));
                            retryCount++;
                        } else {
                            break;
                        }
                    }
                }

                // If we get here, all retries failed
                console.error('❌ ETH Test Mode: Hot wallet transaction failed after all retries:', lastError);
                return {
                    success: false,
                    reason: 'hot_wallet_transaction_failed',
                    message: `Failed to send ETH from hot wallet: ${lastError.message}`
                };
            } else {
                // Other rewards (like creator rewards) use the recipient from metadata
                const recipientAddress = metadata?.creatorUserId || metadata?.recipientAddress;
                console.log(`🧪 ETH Test Mode: ${reason} reward - sending ${amount} ETH to ${recipientAddress}`);

                if (!recipientAddress) {
                    console.error('❌ ETH Test Mode: No recipient address found');
                    return {
                        success: false,
                        reason: 'no_recipient_address',
                        message: 'No recipient address found for ETH transfer'
                    };
                }
                // For non-level-completion rewards, handle them with the old player-to-player logic
                // This is for creator rewards, etc. where the player pays the creator
                try {
                    // Convert amount from ETH to wei (18 decimal places)
                    const amountInWei = BigInt(Math.round(amount * 1e18)).toString();

                    // Get current gas price - handle both browser and Node.js environments
                    let gasPrice, nonce, chainId;
                    if (this.walletProvider && typeof this.walletProvider.request === 'function') {
                        // Browser environment (MetaMask)
                        gasPrice = await this.walletProvider.request({
                            method: 'eth_gasPrice'
                        });
                        nonce = await this.walletProvider.request({
                            method: 'eth_getTransactionCount',
                            params: [this.walletAddress, 'latest']
                        });
                        chainId = await this.walletProvider.request({ method: 'eth_chainId' });
                    } else {
                        // Node.js environment (ethers.js provider)
                        const gasPriceResult = await this.walletProvider.getFeeData();
                        gasPrice = '0x' + gasPriceResult.gasPrice.toString(16);
                        const nonceResult = await this.walletProvider.getTransactionCount(this.walletAddress, 'latest');
                        nonce = '0x' + nonceResult.toString(16);
                        const network = await this.walletProvider.getNetwork();
                        chainId = '0x' + network.chainId.toString(16);
                    }

                    // Estimate gas limit for a simple transfer
                    const gasLimit = '0x5208'; // 21000 gas for simple transfer

                    // Create transaction parameters to send to recipient
                    const transactionParams = {
                        from: this.walletAddress,
                        to: recipientAddress, // Send to actual recipient address
                        value: '0x' + BigInt(amountInWei).toString(16),
                        gasPrice: gasPrice,
                        gas: gasLimit,
                        nonce: nonce,
                        chainId: chainId
                    };

                    console.log('🚀 ETH Test Mode: Sending transaction with params:', transactionParams);

                    // Send transaction - handle both browser and Node.js environments
                    let transactionHash;
                    if (this.walletProvider && typeof this.walletProvider.request === 'function') {
                        // Browser environment (MetaMask)
                        transactionHash = await this.walletProvider.request({
                            method: 'eth_sendTransaction',
                            params: [transactionParams]
                        });
                    } else if (this.walletInstance) {
                        // Node.js environment - use wallet instance to sign and send
                        const tx = {
                            to: recipientAddress,
                            value: amountInWei,
                            gasPrice: gasPrice,
                            gasLimit: gasLimit,
                            nonce: nonce,
                            chainId: chainId
                        };

                        const txResponse = await this.walletInstance.sendTransaction(tx);
                        transactionHash = txResponse.hash;

                        console.log('🚀 ETH Test Mode: Transaction sent via wallet instance! Hash:', transactionHash);
                    } else {
                        throw new Error('No wallet provider or wallet instance available for ETH transactions');
                    }

                    console.log('✅ ETH Test Mode: Transaction sent! Hash:', transactionHash);

                    // Update wallet balance after successful transaction
                    await this.syncWalletBalance();

                    const transaction = {
                        id: this.generateTransactionId(),
                        type: 'earned',
                        amount: amount,
                        reason: reason,
                        timestamp: Date.now(),
                        balanceAfter: this.walletBalance,
                        hash: transactionHash,
                        metadata: {
                            ...metadata,
                            testMode: true,
                            walletBalance: this.walletBalance,
                            recipientAddress: recipientAddress,
                            transactionHash: transactionHash
                        }
                    };

                    this.addTransaction(transaction);
                    this.updatePerformanceMetrics(reason, metadata);
                    this.addPendingReward(amount, reason);
                    this.triggerBalanceUpdate();
                    this.triggerRewardEarned(amount, reason, metadata);

                    return {
                        success: true,
                        transaction: transaction,
                        newBalance: this.walletBalance,
                        transactionHash: transactionHash,
                        recipientAddress: recipientAddress
                    };

                } catch (error) {
                    console.error('❌ ETH Test Mode: Transaction failed:', error);
                    return {
                        success: false,
                        reason: 'eth_transaction_failed',
                        message: `ETH transaction failed: ${error.message}`
                    };
                }
            }
        } else {
            // Traditional WISH token system
            this.playerBalance += amount;
            this.totalEarned += amount;
            
            const transaction = {
                id: this.generateTransactionId(),
                type: 'earned',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                balanceAfter: this.playerBalance,
                metadata: metadata
            };
            
            this.addTransaction(transaction);
            this.updatePerformanceMetrics(reason, metadata);
            this.addPendingReward(amount, reason);
            this.saveToStorage();
            this.triggerBalanceUpdate();
            this.triggerRewardEarned(amount, reason, metadata);
            
            return {
                success: true,
                transaction: transaction,
                newBalance: this.playerBalance
            };
        }
    }

    /**
     * Verify wallet balance and sync with local balance
     * @private
     * @returns {Promise<boolean>} True if wallet verification successful
     */
    async verifyWalletBalance() {
        if (!this.walletConnected && !this.debugMode) {
            console.warn('Wallet not connected - cannot verify balance');
            return false;
        }

        try {
            await this.syncWalletBalance();
            
            // In production, we would compare wallet balance with local balance
            // and potentially sync them if needed
            if (this.debugMode) {
                // In debug mode, ensure localStorage reflects wallet balance (browser only)
                if (typeof localStorage !== 'undefined') {
                    localStorage.setItem('debugWalletBalance', this.walletBalance.toString());
                }
            }
            
            return true;
        } catch (error) {
            console.error('Failed to verify wallet balance:', error);
            return false;
        }
    }
    
    /**
     * Spend tokens from player balance (with wallet integration)
     * @param {number} amount - Amount to spend
     * @param {string} reason - Reason for spending
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     */
    async spendTokens(amount, reason, metadata = {}) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }
        
        // Only use ETH wallet transactions if ETH test mode is actually enabled
        if (typeof window !== 'undefined' && window.gameEngine?.ethTestModeManager?.isTestMode) {
            await this.syncWalletBalance();
            
            if (this.walletBalance < amount) {
                return {
                    success: false,
                    reason: 'insufficient_balance',
                    required: amount,
                    available: this.walletBalance,
                    hasWallet: this.walletConnected
                };
            }
            
            // Spend directly from wallet - this will process a real ETH transaction
            const walletResult = await this.spendFromWallet(amount, reason, metadata);
            
            if (!walletResult.success) {
                return walletResult;
            }
            
            // Get the updated balance after the transaction
            const actualBalanceAfter = this.walletBalance;
            
            // Update metadata with wallet info
            metadata.walletTransactionId = walletResult.transactionId;
            metadata.walletAmount = amount;
            
            const finalTransaction = {
                id: walletResult.transactionId,
                type: 'spent',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                balanceAfter: actualBalanceAfter,
                metadata: metadata
            };
            
            // Trigger callbacks
            this.triggerBalanceUpdate();
            this.triggerTransaction(finalTransaction);
            
            return {
                success: true,
                transaction: finalTransaction,
                newBalance: actualBalanceAfter
            };
        } else {
            // Use traditional WISH token system
            if (this.playerBalance < amount) {
                return {
                    success: false,
                    reason: 'insufficient_balance',
                    required: amount,
                    available: this.playerBalance
                };
            }
            
            // Deduct from WISH token balance
            this.playerBalance -= amount;
            this.totalSpent += amount;
            
            const transaction = {
                id: this.generateTransactionId(),
                type: 'spent',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                balanceAfter: this.playerBalance,
                metadata: metadata
            };
            
            this.addTransaction(transaction);
            this.saveToStorage();
            
            // Trigger callbacks
            this.triggerBalanceUpdate();
            this.triggerTransaction(transaction);
            
            return {
                success: true,
                transaction: transaction,
                newBalance: this.playerBalance
            };
        }
    }

    /**
     * Spend tokens from connected wallet (supports OrangeID and standard wallets)
     * @param {number} amount - Amount to spend
     * @param {string} reason - Reason for spending
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     * @private
     */
    async spendFromWallet(amount, reason, metadata = {}) {
        if (this.debugMode) {
            // In debug mode, simulate wallet transaction using localStorage
            // Only sync if we don't have a balance set
            if (this.walletBalance === 0) {
                await this.syncWalletBalance();
            }
            
            if (this.walletBalance < amount) {
                return {
                    success: false,
                    reason: 'insufficient_wallet_balance',
                    message: `Insufficient wallet balance. Required: ${amount}, Available: ${this.walletBalance}`
                };
            }
            
            // Deduct the amount from wallet balance (including gas fees)
            const gasFee = 0.00003937; // Approximate gas fee for simple transfer
            const totalCost = amount + gasFee;
            
            if (this.walletBalance < totalCost) {
                return {
                    success: false,
                    reason: 'insufficient_wallet_balance_for_gas',
                    message: `Insufficient wallet balance for amount + gas. Required: ${totalCost}, Available: ${this.walletBalance}`
                };
            }
            
            this.walletBalance -= totalCost;
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('debugWalletBalance', this.walletBalance.toString());
            }
            
            const transactionId = this.generateTransactionId();
            return {
                success: true,
                transactionId: transactionId,
                amount: amount,
                gasFee: gasFee,
                totalCost: totalCost,
                newWalletBalance: this.walletBalance
            };
        }

        if (!this.walletConnected) {
            return {
                success: false,
                reason: 'wallet_not_connected',
                message: 'Wallet not connected'
            };
        }

        try {
            if (this.walletProvider === 'orangeid') {
                // Handle OrangeID wallet transaction - but still use MetaMask for actual transaction
                // Check if MetaMask is available
                if (typeof window !== 'undefined' && window.ethereum && typeof window.ethereum.request === 'function') {
                    // Use MetaMask for the actual transaction, similar to syncOrangeIDWalletBalance
                    console.log('Processing real ETH transaction for OrangeID user:', amount, 'ETH');
                    
                    try {
                        // Convert amount from ETH to wei (18 decimal places)
                        const amountInWei = BigInt(Math.round(amount * 1e18)).toString();
                        
                        // Get current gas price
                        const gasPrice = await window.ethereum.request({
                            method: 'eth_gasPrice'
                        });
                        
                        // Estimate gas limit for a simple transfer
                        const gasLimit = '0x5208'; // 21000 gas for simple transfer
                        
                        // Get the current nonce for the sender
                        const nonce = await window.ethereum.request({
                            method: 'eth_getTransactionCount',
                            params: [this.walletAddress, 'latest']
                        });
                        
                        // Get the recipient address from metadata if available
                        const recipientAddress = metadata?.recipientAddress || metadata?.creatorUserId;
                        
                        if (!recipientAddress) {
                            console.error('❌ ETH Test Mode: No recipient address found in metadata for spending');
                            return {
                                success: false,
                                reason: 'no_recipient_address',
                                message: 'No recipient address found in metadata for ETH transaction'
                            };
                        }
                        
                        // Create transaction parameters to send to actual recipient
                        const transactionParams = {
                            from: this.walletAddress,
                            to: recipientAddress, // Send to actual recipient address, not burn address
                            value: '0x' + BigInt(amountInWei).toString(16),
                            gasPrice: gasPrice,
                            gas: gasLimit,
                            nonce: nonce,
                            chainId: await window.ethereum.request({ method: 'eth_chainId' })
                        };
                        
                        console.log('🚀 Sending ETH transaction to recipient address with params:', transactionParams);
                        
                        // Request transaction signature and sending
                        const transactionHash = await window.ethereum.request({
                            method: 'eth_sendTransaction',
                            params: [transactionParams]
                        });
                        
                        console.log('✅ ETH transaction sent! Hash:', transactionHash);
                        
                        // Wait for transaction confirmation (optional, but recommended)
                        console.log('⏳ Waiting for transaction confirmation...');
                        
                        // Update wallet balance after successful transaction
                        await this.syncWalletBalance();
                        
                        return {
                            success: true,
                            transactionId: transactionHash, // Use real transaction hash
                            amount: amount,
                            newWalletBalance: this.walletBalance,
                            transactionHash: transactionHash,
                            toAddress: recipientAddress
                        };
                        
                    } catch (error) {
                        console.error('❌ ETH transaction failed:', error);
                        return {
                            success: false,
                            reason: 'eth_transaction_failed',
                            error: error.message,
                            message: 'Failed to process ETH transaction: ' + error.message
                        };
                    }
                } else {
                    // Fallback to OrangeID wallet transaction if MetaMask is not available
                    return await this.spendFromOrangeIDWallet(amount, reason, metadata);
                }
            } else if (this.walletProvider && typeof this.walletProvider.request === 'function') {
                // Standard Ethereum wallet transaction - ACTUALLY SEND ETH TO BURN ADDRESS
                console.log('Processing real ETH transaction for:', amount, 'ETH');
                
                try {
                    // Convert amount from ETH to wei (18 decimal places)
                    const amountInWei = BigInt(Math.round(amount * 1e18)).toString();
                    
                    // Get current gas price
                    const gasPrice = await this.walletProvider.request({
                        method: 'eth_gasPrice'
                    });
                    
                    // Estimate gas limit for a simple transfer
                    const gasLimit = '0x5208'; // 21000 gas for simple transfer
                    
                    // Get the current nonce for the sender
                    const nonce = await this.walletProvider.request({
                        method: 'eth_getTransactionCount',
                        params: [this.walletAddress, 'latest']
                    });
                    
                    // Get the recipient address from metadata if available
                    const recipientAddress = metadata?.recipientAddress || metadata?.creatorUserId;
                    
                    if (!recipientAddress) {
                        console.error('❌ ETH Test Mode: No recipient address found in metadata for spending');
                        return {
                            success: false,
                            reason: 'no_recipient_address',
                            message: 'No recipient address found in metadata for ETH transaction'
                        };
                    }
                    
                    // Create transaction parameters to send to actual recipient
                    const transactionParams = {
                        from: this.walletAddress,
                        to: recipientAddress, // Send to actual recipient address, not burn address
                        value: '0x' + BigInt(amountInWei).toString(16),
                        gasPrice: gasPrice,
                        gas: gasLimit,
                        nonce: nonce,
                        chainId: await this.walletProvider.request({ method: 'eth_chainId' })
                    };
                    
                    console.log('🚀 Sending ETH transaction to recipient address with params:', transactionParams);
                    
                    // Request transaction signature and sending
                    const transactionHash = await this.walletProvider.request({
                        method: 'eth_sendTransaction',
                        params: [transactionParams]
                    });
                    
                    console.log('✅ ETH transaction sent! Hash:', transactionHash);
                    
                    // Wait for transaction confirmation (optional, but recommended)
                    console.log('⏳ Waiting for transaction confirmation...');
                    
                    // Update wallet balance after successful transaction
                    await this.syncWalletBalance();
                    
                    return {
                        success: true,
                        transactionId: transactionHash, // Use real transaction hash
                        amount: amount,
                        newWalletBalance: this.walletBalance,
                        transactionHash: transactionHash,
                        toAddress: recipientAddress
                    };
                    
                } catch (error) {
                    console.error('❌ ETH transaction failed:', error);
                    return {
                        success: false,
                        reason: 'eth_transaction_failed',
                        error: error.message,
                        message: 'Failed to process ETH transaction: ' + error.message
                    };
                }
            } else {
                return {
                    success: false,
                    reason: 'unsupported_wallet_provider',
                    message: 'Unsupported wallet provider: ' + this.walletProvider
                };
            }
        } catch (error) {
            console.error('Wallet transaction failed:', error);
            return {
                success: false,
                reason: 'wallet_transaction_failed',
                error: error.message,
                message: 'Failed to process wallet transaction'
            };
        }
    }

    /**
     * Spend tokens from OrangeID wallet
     * @param {number} amount - Amount to spend
     * @param {string} reason - Reason for spending
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     * @private
     */
    async spendFromOrangeIDWallet(amount, reason, metadata = {}) {
        if (typeof window === 'undefined' || !window.AuthManager || !window.AuthManager.token) {
            return {
                success: false,
                reason: 'orangeid_not_available',
                message: 'OrangeID authentication not available'
            };
        }

        try {
            // This is a simplified implementation - in a real scenario, you would:
            // 1. Call OrangeID API to process the transaction
            // 2. Handle the response and update balances
            
            console.log('Processing OrangeID wallet transaction:', amount, 'for reason:', reason);
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Check balance first
            await this.syncWalletBalance();
            if (this.walletBalance < amount) {
                return {
                    success: false,
                    reason: 'insufficient_wallet_balance',
                    message: `Insufficient OrangeID wallet balance. Required: ${amount}, Available: ${this.walletBalance}`
                };
            }
            
            // Simulate successful transaction
            this.walletBalance -= amount;
            
            // Save to localStorage in debug mode (browser only)
            if (this.debugMode && typeof localStorage !== 'undefined') {
                localStorage.setItem('debugWalletBalance', this.walletBalance.toString());
            }
            
            const transactionId = this.generateTransactionId();
            
            return {
                success: true,
                transactionId: transactionId,
                amount: amount,
                newWalletBalance: this.walletBalance,
                provider: 'orangeid'
            };
            
        } catch (error) {
            console.error('OrangeID wallet transaction failed:', error);
            return {
                success: false,
                reason: 'orangeid_transaction_failed',
                error: error.message,
                message: 'Failed to process OrangeID wallet transaction'
            };
        }
    }
    
    /**
     * Check if player can afford a purchase (checks both local and wallet balances)
     * @param {number} amount - Amount to check
     * @returns {boolean} Whether player can afford the amount
     */
    canAfford(amount) {
        // Only check ETH wallet balance if ETH test mode is actually enabled
        if (typeof window !== 'undefined' && window.gameEngine?.ethTestModeManager?.isTestMode) {
            return this.getBalance() >= amount;
        }
        // Otherwise check WISH token balance
        return this.playerBalance >= amount;
    }

    /**
     * Get current balance (wallet only in test mode)
     * @returns {number} Current balance
     */
    getBalance() {
        // Only return ETH wallet balance if ETH test mode is actually enabled
        if (typeof window !== 'undefined' && window.gameEngine?.ethTestModeManager?.isTestMode) {
            return this.walletBalance;
        }
        // Otherwise return WISH token balance
        return this.playerBalance;
    }

    /**
     * Handle level completion with high score verification
     * @param {object} completionData - Level completion data
     * @param {number} levelScore - Score achieved in the level
     * @returns {Promise<object>} Token award result
     */
    /**
     * Create a fallback level manager for Node.js environments
     * @returns {object} Minimal level manager with reward calculation
     */
    createFallbackLevelManager() {
        const GAME_CONFIG = {
            BASE_LEVEL_REWARD: 1250,
            REWARD_SYSTEM: {
                BASE_VARIANCE: 0.4,
                PERFECT_SCORE_VARIANCE: 0.2,
                POOR_SCORE_VARIANCE: 0.6,
                POWER_UP_VARIANCE_REDUCTION: 0.1,
                MAX_VARIANCE_REDUCTION: 0.3,
                HOUSE_EDGE: 0.15,
                MIN_REWARD_RATIO: 0.3,
                MAX_REWARD_CHANCE: 0.10,
                ABOVE_AVERAGE_CHANCE: 0.25,
                AVERAGE_CHANCE: 0.40,
                BELOW_AVERAGE_CHANCE: 0.20,
                MIN_REWARD_CHANCE: 0.05
            }
        };

        return {
            calculateBaseLevelReward: (levelNumber) => {
                return GAME_CONFIG.BASE_LEVEL_REWARD * Math.pow(1.1, levelNumber - 1);
            },

            calculateProbabilisticReward: (levelNumber, activePowerUps = [], completionPercentage = 1.0) => {
                const config = GAME_CONFIG.REWARD_SYSTEM;
                const maxReward = GAME_CONFIG.BASE_LEVEL_REWARD * Math.pow(1.1, levelNumber - 1);

                // Determine variance based on performance
                let variance = config.BASE_VARIANCE;
                if (completionPercentage >= 1.0) {
                    variance = config.PERFECT_SCORE_VARIANCE;
                } else if (completionPercentage < 0.5) {
                    variance = config.POOR_SCORE_VARIANCE;
                }

                // Power-ups reduce variance
                const varianceReduction = Math.min(
                    activePowerUps.length * config.POWER_UP_VARIANCE_REDUCTION,
                    config.MAX_VARIANCE_REDUCTION
                );
                const finalVariance = Math.max(0.1, variance - varianceReduction);

                // Calculate reward range
                const minReward = Math.max(
                    maxReward * config.MIN_REWARD_RATIO,
                    maxReward * (1 - finalVariance)
                );
                const maxPossibleReward = maxReward * (1 + finalVariance * 0.5);

                return {
                    maxReward: maxReward,
                    minReward: minReward,
                    maxPossibleReward: maxReward, // Max possible is now truly the max
                    variance: finalVariance,
                    activePowerUps: activePowerUps.length,
                    completionPercentage: completionPercentage,
                    expectedValue: maxReward * (1 - config.HOUSE_EDGE),
                    maxRewardChance: config.MAX_REWARD_CHANCE,
                    aboveAverageChance: config.ABOVE_AVERAGE_CHANCE,
                    averageChance: config.AVERAGE_CHANCE,
                    belowAverageChance: config.BELOW_AVERAGE_CHANCE,
                    minRewardChance: config.MIN_REWARD_CHANCE
                };
            },

            rollForReward: (rewardData) => {
                const config = GAME_CONFIG.REWARD_SYSTEM;
                const roll = Math.random();

                // Bell curve distribution - no jackpot, max reward is truly the maximum
                if (roll < config.MIN_REWARD_CHANCE) {
                    // 5% chance of minimum reward
                    return Math.floor(rewardData.minReward);
                } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE) {
                    // 20% chance of below average (30-50% of max)
                    const range = rewardData.maxReward * 0.5 - rewardData.minReward;
                    return Math.floor(rewardData.minReward + (Math.random() * range));
                } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE + config.AVERAGE_CHANCE) {
                    // 40% chance of average (50-75% of max)
                    const minRange = rewardData.maxReward * 0.5;
                    const maxRange = rewardData.maxReward * 0.75;
                    return Math.floor(minRange + (Math.random() * (maxRange - minRange)));
                } else if (roll < config.MIN_REWARD_CHANCE + config.BELOW_AVERAGE_CHANCE + config.AVERAGE_CHANCE + config.ABOVE_AVERAGE_CHANCE) {
                    // 25% chance of above average (75-99% of max)
                    const minRange = rewardData.maxReward * 0.75;
                    const maxRange = rewardData.maxReward * 0.99;
                    return Math.floor(minRange + (Math.random() * (maxRange - minRange)));
                } else {
                    // 10% chance of max reward
                    return Math.floor(rewardData.maxReward);
                }
            }
        };
    }

    /**
     * Handle level completion with new daily reward system
     * @param {object} completionData - Level completion data
     * @param {number} levelScore - Score achieved in the level
     * @param {object} levelConfig - Level configuration with enemy data
     * @returns {Promise<object>} Token award result
     */
    async handleLevelCompletion(completionData, levelScore, levelConfig = null, activePowerUps = []) {
        // Check daily limits first
        if (!this.dailyRewardTracker.canEarnReward(completionData.levelNumber)) {
            return {
                success: false,
                reason: 'level_already_completed_today',
                message: 'Level already completed today',
                tokensAwarded: 0
            };
        }

        if (this.dailyRewardTracker.isDailyLimitReached()) {
            return {
                success: false,
                reason: 'daily_limit_reached',
                message: 'Daily earning limit reached',
                tokensAwarded: 0
            };
        }

        // Calculate completion percentage
        const completionPercentage = levelConfig?.totalEnemies > 0 ?
            completionData.enemiesDefeated / levelConfig.totalEnemies : 1.0;

        // Get probabilistic reward calculation from LevelManager
        // Handle both browser and Node.js environments
        let levelManager = null;
        if (typeof window !== 'undefined' && window.gameEngine?.levelManager) {
            levelManager = window.gameEngine.levelManager;
        }

        if (!levelManager) {
            // Fallback for Node.js environment - create a minimal reward calculator
            levelManager = this.createFallbackLevelManager();
        }

        const rewardCalc = levelManager.calculateProbabilisticReward(
            completionData.levelNumber,
            activePowerUps,
            completionPercentage
        );

        // Roll for reward (simulate the chance)
        const roll = Math.random();
        let actualReward = 0;
        let rewardType = 'none';

        if (roll < rewardCalc.jackpotChance) {
            // Jackpot!
            actualReward = rewardCalc.jackpotReward;
            rewardType = 'jackpot';
        } else if (roll < rewardCalc.successChance) {
            // Normal success
            actualReward = rewardCalc.maxPossibleReward;
            rewardType = 'success';
        } else if (roll < (rewardCalc.successChance + rewardCalc.bustChance)) {
            // Bust - no reward
            actualReward = 0;
            rewardType = 'bust';
        } else {
            // Partial reward (between bust and success)
            const partialMultiplier = 0.3 + (Math.random() * 0.4); // 30-70% of max
            actualReward = Math.floor(rewardCalc.maxPossibleReward * partialMultiplier);
            rewardType = 'partial';
        }

        // Apply daily capacity limit
        const remainingCapacity = this.dailyRewardTracker.getRemainingDailyCapacity();
        actualReward = Math.min(actualReward, remainingCapacity);

        if (actualReward <= 0) {
            return {
                success: false,
                reason: rewardType === 'bust' ? 'bad_luck' : 'no_reward_due_to_limits',
                message: rewardType === 'bust' ? 'Bad luck! No reward this time.' : 'No reward due to daily limits',
                tokensAwarded: 0,
                rewardType: rewardType,
                rewardCalculation: rewardCalc
            };
        }

        // Apply ETH test mode discount if enabled
        const isEthTestMode = (typeof window !== 'undefined') ?
            window.gameEngine?.ethTestModeManager?.isTestMode :
            (this.ethTestModeManager?.isTestMode || false);

        if (isEthTestMode && this.ethTestModeManager) {
            actualReward = this.ethTestModeManager.applyTestModeDiscount(actualReward);
            console.log(`🧪 ETH Test Mode: Applied 90% discount to reward: ${actualReward} ETH`);
        }

        // Award the tokens
        const result = await this.awardTokens(
            actualReward,
            'level_completion',
            {
                score: levelScore,
                levelNumber: completionData.levelNumber,
                completionPercentage: completionPercentage,
                rewardType: rewardType,
                powerUpsUsed: activePowerUps.length,
                roll: roll,
                rewardCalculation: rewardCalc
            }
        );

        if (result.success) {
            // Record in daily tracker
            this.dailyRewardTracker.recordLevelCompletion(
                completionData.levelNumber,
                completionPercentage,
                actualReward
            );

            const currency = isEthTestMode ? 'ETH' : 'tokens';
            console.log(`✅ Level ${completionData.levelNumber} completed! ${rewardType.toUpperCase()}: ${actualReward} ${currency}`);
            return {
                success: true,
                tokensAwarded: actualReward,
                newBalance: this.playerBalance,
                rewardType: rewardType,
                rewardCalculation: rewardCalc,
                transaction: result.transaction
            };
        } else {
            console.error('Failed to award level completion tokens:', result);
            return {
                success: false,
                reason: result.reason,
                message: 'Failed to process token award'
            };
        }
    }

    /**
     * Sync local tokens to wallet (transfer from local to blockchain)
     * @param {number} amount - Amount to sync to wallet
     * @returns {Promise<object>} Sync result
     */
    async syncToWallet(amount) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }

        if (amount > this.playerBalance) {
            return {
                success: false,
                reason: 'insufficient_local_balance',
                required: amount,
                available: this.playerBalance
            };
        }

        if (!this.walletConnected && !this.debugMode) {
            return {
                success: false,
                reason: 'wallet_not_connected',
                message: 'Connect wallet to sync tokens'
            };
        }

        try {
            // Deduct from local balance
            this.playerBalance -= amount;
            this.totalSpent += amount; // Count as spent for accounting

            // In debug mode, simulate wallet deposit
            if (this.debugMode) {
                await this.syncWalletBalance();
                this.walletBalance += amount;
                if (typeof localStorage !== 'undefined') {
                    localStorage.setItem('debugWalletBalance', this.walletBalance.toString());
                }
                
                const transaction = {
                    id: this.generateTransactionId(),
                    type: 'wallet_sync',
                    amount: amount,
                    reason: 'local_to_wallet_sync',
                    timestamp: Date.now(),
                    balanceAfter: this.playerBalance,
                    metadata: {
                        walletBalanceAfter: this.walletBalance,
                        source: 'local_sync'
                    }
                };
                this.addTransaction(transaction);
                this.saveToStorage();
                this.triggerBalanceUpdate();
                
                return {
                    success: true,
                    amount: amount,
                    newLocalBalance: this.playerBalance,
                    newWalletBalance: this.walletBalance
                };
            }

            // Real implementation would involve blockchain transaction here
            console.log('Simulating blockchain transfer of', amount, 'tokens to wallet');
            
            const transactionId = this.generateTransactionId();
            const transaction = {
                id: transactionId,
                type: 'wallet_sync',
                amount: amount,
                reason: 'local_to_wallet_sync',
                timestamp: Date.now(),
                balanceAfter: this.playerBalance,
                metadata: {
                    transactionId: transactionId,
                    source: 'blockchain_sync'
                }
            };
            this.addTransaction(transaction);
            this.saveToStorage();
            this.triggerBalanceUpdate();
            
            // Sync wallet balance after transfer
            await this.syncWalletBalance();
            
            return {
                success: true,
                amount: amount,
                newLocalBalance: this.playerBalance,
                newWalletBalance: this.walletBalance,
                transactionId: transactionId
            };

        } catch (error) {
            console.error('Failed to sync tokens to wallet:', error);
            
            // Rollback local balance on error
            this.playerBalance += amount;
            this.totalSpent -= amount;
            
            return {
                success: false,
                reason: 'sync_failed',
                error: error.message,
                message: 'Failed to sync tokens to wallet'
            };
        }
    }
    
    /**
     * Get token economy statistics
     * @returns {object} Economy statistics
     */
    /**
     * Get token economy statistics including daily progress
     * @returns {object} Economy statistics
     */
    getStatistics() {
        const netProfit = this.totalEarned - this.totalSpent;
        const averageEarningPerLevel = this.performanceMetrics.levelsCompleted > 0
            ? this.totalEarned / this.performanceMetrics.levelsCompleted
            : 0;
        
        return {
            currentBalance: this.playerBalance,
            totalEarned: this.totalEarned,
            totalSpent: this.totalSpent,
            netProfit: netProfit,
            transactionCount: this.transactionHistory.length,
            averageEarningPerLevel: Math.floor(averageEarningPerLevel),
            performanceMetrics: { ...this.performanceMetrics },
            dailyProgress: this.dailyRewardTracker.getDailyProgress()
        };
    }
    
    /**
     * Get recent transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Recent transactions
     */
    getRecentTransactions(count = 10) {
        return this.transactionHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Add transaction to history
     * @param {object} transaction - Transaction to add
     */
    addTransaction(transaction) {
        this.transactionHistory.push(transaction);
        
        // Limit history size
        if (this.transactionHistory.length > this.maxHistorySize) {
            this.transactionHistory.shift();
        }
    }
    
    /**
     * Generate unique transaction ID
     * @returns {string} Transaction ID
     */
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Update performance metrics
     * @param {string} reason - Reason for the update
     * @param {object} metadata - Additional metadata
     */
    updatePerformanceMetrics(reason, metadata) {
        if (reason === 'level_completion') {
            this.performanceMetrics.levelsCompleted++;
            
            if (metadata.score) {
                this.performanceMetrics.totalScore += metadata.score;
            }
            
            if (metadata.completionTime) {
                // Update average completion time
                const currentAvg = this.performanceMetrics.averageCompletionTime;
                const count = this.performanceMetrics.levelsCompleted;
                this.performanceMetrics.averageCompletionTime = 
                    (currentAvg * (count - 1) + metadata.completionTime) / count;
            }
            
            if (metadata.bonuses) {
                if (metadata.bonuses.perfect) this.performanceMetrics.perfectCompletions++;
                if (metadata.bonuses.speed) this.performanceMetrics.speedBonuses++;
                if (metadata.bonuses.accuracy) this.performanceMetrics.accuracyBonuses++;
            }
        }
    }
    
    /**
     * Add pending reward for visual feedback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reason for reward
     */
    addPendingReward(amount, reason) {
        const reward = {
            id: this.generateTransactionId(),
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            displayed: false
        };
        
        this.pendingRewards.push(reward);
    }
    
    /**
     * Get pending rewards for display
     * @returns {Array} Pending rewards
     */
    getPendingRewards() {
        return this.pendingRewards.filter(reward => !reward.displayed);
    }
    
    /**
     * Mark reward as displayed
     * @param {string} rewardId - Reward ID to mark as displayed
     */
    markRewardDisplayed(rewardId) {
        const reward = this.pendingRewards.find(r => r.id === rewardId);
        if (reward) {
            reward.displayed = true;
        }
        
        // Clean up old displayed rewards
        const cutoffTime = Date.now() - 10000; // 10 seconds
        this.pendingRewards = this.pendingRewards.filter(
            reward => !reward.displayed || reward.timestamp > cutoffTime
        );
    }
    
    /**
     * Update reward animations
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateRewardAnimations(deltaTime) {
        // Update existing animations
        for (let i = this.rewardAnimations.length - 1; i >= 0; i--) {
            const animation = this.rewardAnimations[i];
            animation.elapsed += deltaTime;
            
            // Remove completed animations
            if (animation.elapsed >= animation.duration) {
                this.rewardAnimations.splice(i, 1);
            }
        }
        
        // Add new animations for pending rewards
        const pendingRewards = this.getPendingRewards();
        for (const reward of pendingRewards) {
            this.rewardAnimations.push({
                id: reward.id,
                amount: reward.amount,
                reason: reward.reason,
                startTime: Date.now(),
                elapsed: 0,
                duration: 3000, // 3 second animation
                startY: 100,
                endY: 50,
                alpha: 1.0
            });
            
            this.markRewardDisplayed(reward.id);
        }
    }
    
    /**
     * Render token balance and reward animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} deltaTime - Time elapsed since last update
     */
    render(ctx, deltaTime) {
        // Update animations
        this.updateRewardAnimations(deltaTime);
    }
    
    /**
     * Render token balance display
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderTokenBalance(ctx) {
        const x = ctx.canvas.width - 20;
        const y = 80;
        const panelWidth = 160;
        const panelHeight = 40;
        const panelX = x - panelWidth - 10;
        const panelY = y - panelHeight / 2;
        
        // Create sci-fi background panel with gradient
        const panelGradient = ctx.createLinearGradient(panelX, panelY, panelX + panelWidth, panelY + panelHeight);
        panelGradient.addColorStop(0, 'rgba(30, 0, 46, 0.8)');
        panelGradient.addColorStop(1, 'rgba(10, 0, 30, 0.8)');
        ctx.fillStyle = panelGradient;
        ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
        
        // Panel border with sci-fi glow
        ctx.strokeStyle = '#ff00ff';
        ctx.lineWidth = 2;
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 15;
        ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
        
        // Inner border for depth
        ctx.strokeStyle = 'rgba(255, 0, 255, 0.3)';
        ctx.lineWidth = 1;
        ctx.strokeRect(panelX + 5, panelY + 5, panelWidth - 10, panelHeight - 10);
        ctx.shadowBlur = 0;
        
        // Token icon (simple hexagon)
        const iconSize = 12;
        const iconX = panelX + 15;
        const iconY = panelY + panelHeight / 2;
        ctx.fillStyle = '#ff00ff';
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (Math.PI / 3) * i;
            const pointX = iconX + iconSize * Math.cos(angle);
            const pointY = iconY + iconSize * Math.sin(angle);
            if (i === 0) {
                ctx.moveTo(pointX, pointY);
            } else {
                ctx.lineTo(pointX, pointY);
            }
        }
        ctx.closePath();
        ctx.fill();
        ctx.shadowBlur = 0;
        
        // Token text with sci-fi styling
        ctx.fillStyle = '#ff00ff';
        ctx.font = 'bold 16px "Courier New", monospace';
        ctx.textAlign = 'right';
        ctx.shadowColor = '#ff00ff';
        ctx.shadowBlur = 8;
        
        // Use getBalanceDisplay if available, otherwise fall back to playerBalance
        const balanceDisplay = this.getBalanceDisplay ? this.getBalanceDisplay() : `WISH: ${this.playerBalance}`;
        ctx.fillText(balanceDisplay, x - 5, y + 5);
        ctx.shadowBlur = 0;
    }
    
    
    /**
     * Format reward reason for display
     * @param {string} reason - Internal reason code
     * @returns {string} Display-friendly reason
     */
    formatRewardReason(reason) {
        const reasonMap = {
            'level_completion': 'Level Complete',
            'speed_bonus': 'Speed Bonus',
            'accuracy_bonus': 'Accuracy Bonus',
            'perfect_bonus': 'Perfect Clear',
            'daily_bonus': 'Daily Bonus',
            'achievement': 'Achievement'
        };
        
        return reasonMap[reason] || reason;
    }
    
    /**
     * Reset token economy (for new game)
     */
    reset() {
        this.playerBalance = 0;
        this.totalEarned = 0;
        this.totalSpent = 0;
        this.transactionHistory = [];
        this.pendingRewards = [];
        this.rewardAnimations = [];
        
        this.performanceMetrics = {
            levelsCompleted: 0,
            totalScore: 0,
            averageCompletionTime: 0,
            perfectCompletions: 0,
            speedBonuses: 0,
            accuracyBonuses: 0
        };
        
        
    }
    
    /**
     * Trigger balance update callback
     */
    triggerBalanceUpdate() {
        if (this.onBalanceUpdateCallback) {
            this.onBalanceUpdateCallback(this.playerBalance);
        }
    }
    
    /**
     * Trigger transaction callback
     * @param {object} transaction - Transaction data
     */
    triggerTransaction(transaction) {
        if (this.onTransactionCallback) {
            this.onTransactionCallback(transaction);
        }
    }
    
    /**
     * Trigger reward earned callback
     * @param {number} amount - Reward amount
     * @param {string} reason - Reward reason
     * @param {object} metadata - Additional metadata
     */
    triggerRewardEarned(amount, reason, metadata) {
        if (this.onRewardEarnedCallback) {
            this.onRewardEarnedCallback(amount, reason, metadata);
        }
    }
    
    /**
     * Set balance update callback
     * @param {function} callback - Callback function
     */
    setOnBalanceUpdate(callback) {
        this.onBalanceUpdateCallback = callback;
    }
    
    /**
     * Set transaction callback
     * @param {function} callback - Callback function
     */
    setOnTransaction(callback) {
        this.onTransactionCallback = callback;
    }
    
    /**
     * Set reward earned callback
     * @param {function} callback - Callback function
     */
    setOnRewardEarned(callback) {
        this.onRewardEarnedCallback = callback;
    }
    
    /**
     * Log cost calculation for debugging
     * @private
     * @param {number} cost - Calculated cost (fixed at 25000 for Reality Warp)
     * @param {object} calculationDetails - Details of the calculation
     */
    logCostCalculation(cost, calculationDetails = {}) {
        if (!this.debugMode) return;
        
        const logEntry = {
            timestamp: Date.now(),
            warpType: 'reality_warp',
            cost: cost,
            details: calculationDetails,
            balance: this.playerBalance
        };
        
        
    }
    
    /**
     * Track token transaction
     * @private
     * @param {object} transaction - Transaction data
     */
    trackTransaction(transaction) {
        if (!this.debugMode) return;
    }
    
    /**
     * Log validation result
     * @private
     * @param {string} type - Validation type
     * @param {object} result - Validation result
     */
    logValidation(type, result) {
        if (!this.debugMode) return;
    }
    
    /**
     * Get performance metrics for cost calculations
     * @returns {object} Cost calculation metrics
     */
    getCostCalculationMetrics() {
        return {
            recentTransactions: this.getRecentTransactions(5)
                .map(tx => ({
                    type: tx.type,
                    amount: tx.amount,
                    reason: tx.reason,
                    timestamp: tx.timestamp
                })),
            currentBalance: this.playerBalance,
            performanceMetrics: { ...this.performanceMetrics }
        };
    }
    
    /**
     * Get transaction metrics for analysis
     * @returns {object} Transaction metrics
     */
    getTransactionMetrics() {
        const earnedTransactions = this.transactionHistory.filter(tx => tx.type === 'earned');
        const spentTransactions = this.transactionHistory.filter(tx => tx.type === 'spent');
        
        return {
            totalTransactions: this.transactionHistory.length,
            earnedTransactions: earnedTransactions.length,
            spentTransactions: spentTransactions.length,
            averageEarnAmount: earnedTransactions.length > 0 
                ? earnedTransactions.reduce((sum, tx) => sum + tx.amount, 0) / earnedTransactions.length 
                : 0,
            averageSpendAmount: spentTransactions.length > 0 
                ? spentTransactions.reduce((sum, tx) => sum + tx.amount, 0) / spentTransactions.length 
                : 0
        };
    }
    
    /**
     * Get all performance metrics
     * @returns {object} All performance metrics
     */
    getAllPerformanceMetrics() {
        return {
            tokenMetrics: this.getStatistics(),
            transactionMetrics: this.getTransactionMetrics(),
            costCalculationMetrics: this.getCostCalculationMetrics(),
            systemState: {
                debugMode: this.debugMode,
                pendingRewards: this.pendingRewards.length,
                activeAnimations: this.rewardAnimations.length
            }
        };
    }
    
    /**
     * Set debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;

    }

    /**
     * Load token data from localStorage (browser only)
     */
    loadFromStorage() {
        try {
            // Only use localStorage in browser environment
            if (typeof localStorage === 'undefined') {
                return;
            }

            const data = localStorage.getItem('tokenEconomyManager');
            if (!data) return;

            const parsed = JSON.parse(data);

            this.playerBalance = parsed.playerBalance || 0;
            this.totalEarned = parsed.totalEarned || 0;
            this.totalSpent = parsed.totalSpent || 0;
            this.transactionHistory = parsed.transactionHistory || [];
            this.performanceMetrics = { ...this.performanceMetrics, ...(parsed.performanceMetrics || {}) };

            console.log(`Loaded token data: ${this.playerBalance} WISH tokens`);
        } catch (error) {
            console.warn('Failed to load token data from localStorage:', error);
        }
    }

    /**
     * Save token data to localStorage (browser only)
     */
    saveToStorage() {
        try {
            // Only use localStorage in browser environment
            if (typeof localStorage === 'undefined') {
                return;
            }

            const data = {
                playerBalance: this.playerBalance,
                totalEarned: this.totalEarned,
                totalSpent: this.totalSpent,
                transactionHistory: this.transactionHistory.slice(-this.maxHistorySize), // Keep only recent transactions
                performanceMetrics: this.performanceMetrics
            };

            localStorage.setItem('tokenEconomyManager', JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save token data to localStorage:', error);
        }
    }
    
    /**
     * Export debug data
     * @returns {object} Debug data
     */
    exportDebugData() {
        return {
            balance: this.playerBalance,
            totals: {
                earned: this.totalEarned,
                spent: this.totalSpent
            },
            recentTransactions: this.getRecentTransactions(10),
            performanceMetrics: { ...this.performanceMetrics },
            pendingRewards: [...this.pendingRewards],
            activeAnimations: [...this.rewardAnimations]
        };
    }
    
    /**
     * Log system state
     */
    logSystemState() {
        if (!this.debugMode) return;
    }
    
    /**
     * Get fixed cost for Reality Warp (25000 WISH Tokens)
     * Reality Warp has fixed cost - no type or level variations
     * @returns {number} Fixed cost amount
     */
    getRealityWarpCost() {
        return 25000; // Fixed cost of 25000 WISH Tokens
    }
    
    /**
     * Apply session discount to cost
     * @param {number} baseCost - Base cost amount
     * @param {number} warpCount - Number of warps used in current session
     * @returns {number} Discounted cost
     */
    applySessionDiscount(baseCost, warpCount = 0) {
        // No discount for first warp, increasing discount for subsequent warps
        const discountRate = Math.min(0.5, warpCount * 0.1); // Max 50% discount
        const discountedCost = baseCost * (1 - discountRate);
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return Math.floor(discountedCost);
    }
    
    /**
     * Apply performance multiplier to cost
     * @param {number} cost - Current cost
     * @param {object} playerStats - Player performance statistics
     * @returns {number} Adjusted cost
     */
    applyPerformanceMultiplier(cost, playerStats) {
        // Better performance = lower cost
        const accuracyMultiplier = Math.max(0.5, 1.0 - (playerStats.accuracy - 0.5) * 0.5);
        const scoreMultiplier = Math.max(0.7, 1.0 - (playerStats.score / 50000) * 0.3);
        const enemiesMultiplier = Math.max(0.8, 1.0 - (playerStats.enemiesDefeated / 100) * 0.2);
        
        const finalMultiplier = accuracyMultiplier * scoreMultiplier * enemiesMultiplier;
        const adjustedCost = cost * finalMultiplier;
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return Math.floor(adjustedCost);
    }
    
    /**
     * Get final cost for warp operation
     * @param {number} level - Current level
     * @param {number} warpCount - Number of warps used in session
     * @param {object} playerStats - Player performance statistics
     * @returns {object} Final cost calculation
     */
    /**
     * Get fixed cost for Reality Warp (25000 WISH Tokens)
     * Reality Warp has fixed cost - no calculations or adjustments
     * @returns {object} Fixed cost and calculation details
     */
    getRealityWarpFinalCost() {
        const fixedCost = this.getRealityWarpCost();
        
        const calculationDetails = {
            cost: fixedCost,
            reason: 'fixed_cost',
            note: 'Reality Warp has fixed cost of 25000 WISH Tokens'
        };
        
        this.logCostCalculation('reality_warp', fixedCost, calculationDetails);
        
        return {
            cost: fixedCost,
            details: calculationDetails
        };
    }
    
    /**
     * Validate token balance for warp operation
     * @param {number} cost - Cost of operation
     * @returns {object} Validation result
     */
    validateTokenBalance(cost) {
        const hasBalance = this.canAfford(cost);
        
        const result = {
            isValid: hasBalance,
            reason: hasBalance ? 'valid' : 'insufficient_balance',
            message: hasBalance 
                ? 'Sufficient tokens for operation' 
                : `Insufficient tokens. Required: ${cost}, Available: ${this.playerBalance}`,
            required: cost,
            available: this.playerBalance
        };
        
        this.logValidation('Token Balance', result);
        return result;
    }
    
    /**
     * Process Reality Warp transaction (fixed cost of 25000)
     * @param {number} cost - Cost of operation (should be 25000)
     * @returns {object} Transaction result
     */
    processWarpTransaction(cost) {
        const validation = this.validateTokenBalance(cost);
        
        if (!validation.isValid) {
            return {
                success: false,
                reason: validation.reason,
                message: validation.message,
                validation: validation
            };
        }
        
        // Create pre-transaction snapshot for potential rollback
        const preTransaction = {
            balance: this.playerBalance,
            totalSpent: this.totalSpent
        };
        
        // Process the transaction
        const spendResult = this.spendTokens(cost, 'reality_warp', {
            warpType: 'reality_warp',
            costBreakdown: cost,
            note: 'Reality Warp transaction'
        });
        
        if (!spendResult.success) {
            return {
                success: false,
                reason: spendResult.reason,
                message: 'Failed to process token transaction'
            };
        }
        
        // Create transaction record for RealityWarpManager
        const transaction = {
            id: spendResult.transaction.id,
            type: 'warp',
            warpType: 'reality_warp',
            cost: cost,
            timestamp: Date.now(),
            balanceAfter: this.playerBalance,
            metadata: {
                preTransactionBalance: preTransaction.balance,
                validation: validation
            }
        };
        
        if (this.debugMode) {
            // Debug information removed
        }
        
        return {
            success: true,
            transaction: transaction,
            newBalance: this.playerBalance
        };
    }
    
    /**
     * Refund Reality Warp transaction
     * @param {number} cost - Cost to refund
     * @param {string} reason - Reason for refund
     * @returns {object} Refund result
     */
    refundWarpTransaction(cost, reason = 'warp_refund') {
        if (cost <= 0) {
            return {
                success: false,
                reason: 'invalid_amount',
                message: 'Refund amount must be positive'
            };
        }
        
        const awardResult = this.awardTokens(cost, 'warp_refund_reality_warp', {
            originalCost: cost,
            refundReason: reason,
            warpType: 'reality_warp'
        });
        
        if (!awardResult.success) {
            return {
                success: false,
                reason: awardResult.reason,
                message: 'Failed to process token refund'
            };
        }
        
        return {
            success: true,
            transaction: awardResult.transaction,
            newBalance: this.playerBalance,
            refundedAmount: cost
        };
    }
    
    /**
     * Rollback transaction
     * @param {object} transaction - Transaction to rollback
     * @returns {object} Rollback result
     */
    rollbackTransaction(transaction) {
        if (transaction.type === 'spent') {
            // Refund the spent amount
            return this.refundWarpTransaction(
                transaction.amount,
                'transaction_rollback'
            );
        } else if (transaction.type === 'earned') {
            // Deduct the awarded amount
            const spendResult = this.spendTokens(
                transaction.amount,
                'rollback_earned',
                { originalTransactionId: transaction.id }
            );
            
            if (!spendResult.success) {
                return {
                    success: false,
                    reason: spendResult.reason,
                    message: 'Failed to rollback earned transaction'
                };
            }
            
            return {
                success: true,
                transaction: spendResult.transaction,
                newBalance: this.playerBalance
            };
        }
        
        return {
            success: false,
            reason: 'invalid_transaction_type',
            message: 'Cannot rollback transaction type: ' + transaction.type
        };
    }
    
    /**
     * Get transaction history for RealityWarpManager
     * @returns {Array} Filtered transaction history
     */
    getWarpTransactionHistory() {
        return this.transactionHistory
            .filter(tx => tx.type === 'spent' && tx.reason === 'reality_warp')
            .reverse();
    }
    
    /**
     * Get warp cost statistics
     * @returns {object} Cost statistics
     */
    getWarpCostStatistics() {
        const warpTransactions = this.getWarpTransactionHistory();
        
        if (warpTransactions.length === 0) {
            return {
                totalWarps: 0,
                averageCost: 0,
                totalSpent: 0,
                mostExpensive: 0,
                leastExpensive: 0
            };
        }
        
        const costs = warpTransactions.map(tx => tx.amount);
        const totalSpent = costs.reduce((sum, cost) => sum + cost, 0);
        
        return {
            totalWarps: warpTransactions.length,
            averageCost: Math.floor(totalSpent / warpTransactions.length),
            totalSpent: totalSpent,
            mostExpensive: Math.max(...costs),
            leastExpensive: Math.min(...costs)
        };
    }
    
    /**
     * Get transaction history
     * @param {number} count - Number of recent transactions to return
     * @returns {Array} Transaction history
     */
    getTransactionHistory(count = 20) {
        return this.transactionHistory
            .slice(-count)
            .reverse(); // Most recent first
    }
    
    /**
     * Deduct tokens for Reality Warp with proper validation (alias for spendTokens)
     * @param {number} cost - Cost to deduct (should be 25000)
     * @returns {object} Transaction result
     */
    deductTokens(cost) {
        return this.spendTokens(cost, 'reality_warp', { warpType: 'reality_warp' });
    }
    
    /**
     * Send tokens from hot wallet to user's wallet after level completion
     * @param {number} amount - Amount to send from hot wallet to user
     * @param {string} sessionId - Current game session ID
     * @returns {Promise<object>} Transaction result
     */
    async sendFromHotWallet(amount, sessionId) {
        if (amount <= 0) {
            return { success: false, reason: 'invalid_amount' };
        }

        // Check if wallet is connected
        if (!this.walletConnected && !this.debugMode) {
            return {
                success: false,
                reason: 'wallet_not_connected',
                message: 'Please connect your wallet to receive tokens'
            };
        }

        try {
            // For real blockchain transactions, send ETH from hot wallet to user
            if (this.walletProvider && this.walletProvider.request && !this.debugMode) {
                console.log(`💰 Sending ${amount} ETH from hot wallet ${this.hotWalletAddress} to user ${this.walletAddress}`);
                
                // Convert ETH to wei
                const amountInWei = BigInt(Math.floor(amount * 1e18));
                const amountHex = '0x' + amountInWei.toString(16);
                
                // Send transaction from hot wallet to user
                const transactionHash = await this.sendTransactionFromHotWallet(this.walletAddress, amount);
                
                if (transactionHash) {
                    console.log(`✅ Successfully sent ${amount} ETH from hot wallet. Transaction: ${transactionHash}`);
                    
                    // Add tokens to player balance (from hot wallet)
                    this.playerBalance += amount;
                    this.totalEarned += amount;

                    // Create hot wallet transaction record
                    const transactionId = this.generateTransactionId();
                    const transaction = {
                        id: transactionId,
                        type: 'hot_wallet_transfer',
                        amount: amount,
                        reason: 'level_completion_from_hot_wallet',
                        timestamp: Date.now(),
                        balanceAfter: this.playerBalance,
                        hash: transactionHash,
                        metadata: {
                            sessionId: sessionId,
                            transactionType: 'hot_wallet_to_user',
                            source: this.hotWalletAddress,
                            destination: this.walletAddress,
                            blockchain: true
                        }
                    };

                    // Add to transaction history
                    this.addTransaction(transaction);
                    this.saveToStorage();
                    this.triggerBalanceUpdate();
                    this.triggerTransaction(transaction);

                    return {
                        success: true,
                        amount: amount,
                        newBalance: this.playerBalance,
                        transactionId: transactionId,
                        blockchainHash: transactionHash
                    };
                } else {
                    throw new Error('Failed to send transaction from hot wallet');
                }
            } else {
                // Debug mode - just update balance without blockchain transaction
                console.log(`🐛 Debug mode: Simulating ${amount} ETH transfer from hot wallet`);
                
                // Add tokens to player balance (from hot wallet)
                this.playerBalance += amount;
                this.totalEarned += amount;

                // Create hot wallet transaction record
                const transactionId = this.generateTransactionId();
                const transaction = {
                    id: transactionId,
                    type: 'hot_wallet_transfer',
                    amount: amount,
                    reason: 'level_completion_from_hot_wallet',
                    timestamp: Date.now(),
                    balanceAfter: this.playerBalance,
                    metadata: {
                        sessionId: sessionId,
                        transactionType: 'hot_wallet_to_user',
                        source: this.hotWalletAddress,
                        destination: this.walletAddress || 'user_wallet',
                        blockchain: false,
                        debug: true
                    }
                };

                // Add to transaction history
                this.addTransaction(transaction);
                this.saveToStorage();
                this.triggerBalanceUpdate();
                this.triggerTransaction(transaction);

                return {
                    success: true,
                    amount: amount,
                    newBalance: this.playerBalance,
                    transactionId: transactionId
                };
            }

        } catch (error) {
            console.error('Failed to send tokens from hot wallet to user:', error);
            
            // Rollback on error
            this.playerBalance -= amount;
            this.totalEarned -= amount;
            
            return {
                success: false,
                reason: 'transfer_failed',
                error: error.message,
                message: 'Failed to send tokens from hot wallet to user'
            };
        }
    }

    /**
     * Send a transaction from the hot wallet to a recipient
     * @param {string} toAddress - Recipient address
     * @param {string} amountHex - Amount in wei (hex string)
     * @returns {Promise<string|null>} Transaction hash or null if failed
     */
    async sendTransactionFromHotWallet(toAddress, amount) {
        try {
            console.log(`🔄 Requesting transaction from hot wallet ${this.hotWalletAddress} to ${toAddress} via secure server API`);
            
            // Call server API to send transaction
            const response = await fetch(`http://localhost:3001/api/wallet/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer secure-token-for-development'
                },
                body: JSON.stringify({
                    toAddress: toAddress,
                    amount: amount.toString(),
                    reason: 'hot_wallet_transaction'
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Server API call failed: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
            }
            
            const result = await response.json();
            console.log(`✅ Transaction sent from hot wallet via server: ${result.transactionHash}`);
            return result.transactionHash;

        } catch (error) {
            console.error('❌ Failed to send transaction from hot wallet via server API:', error);
            return null;
        }
    }

    /**
     * Receive funds to the hot wallet (for purchases, etc.)
     * @param {string} fromAddress - Sender address
     * @param {number} amount - Amount received
     * @param {string} reason - Reason for receiving funds
     * @param {object} metadata - Additional metadata
     * @returns {Promise<object>} Transaction result
     */
    async receiveToHotWallet(fromAddress, amount, reason = 'purchase', metadata = {}) {
        try {
            console.log(`💰 Receiving ${amount} ETH to hot wallet ${this.hotWalletAddress} from ${fromAddress}`);

            // Create transaction record for hot wallet receiving funds
            const transactionId = this.generateTransactionId();
            const transaction = {
                id: transactionId,
                type: 'hot_wallet_receive',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                balanceAfter: this.playerBalance, // This doesn't change player balance
                metadata: {
                    fromAddress: fromAddress,
                    toAddress: this.hotWalletAddress,
                    reason: reason,
                    ...metadata
                }
            };

            // Add to transaction history
            this.addTransaction(transaction);
            this.saveToStorage();
            this.triggerTransaction(transaction);

            console.log(`✅ Recorded ${amount} ETH received to hot wallet from ${fromAddress}`);

            return {
                success: true,
                amount: amount,
                transactionId: transactionId,
                hotWalletAddress: this.hotWalletAddress
            };

        } catch (error) {
            console.error('Failed to record hot wallet receive:', error);
            return {
                success: false,
                reason: 'receive_failed',
                error: error.message,
                message: 'Failed to record hot wallet receive'
            };
        }
    }
    
    /**
     * Helper method to make authenticated API calls to the server
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method (GET, POST, etc.)
     * @param {object} data - Request data (for POST/PUT requests)
     * @returns {Promise<object>} API response
     */
    async makeAuthenticatedApiCall(endpoint, method = 'GET', data = null) {
        try {
            const url = `${this.apiBaseUrl}${endpoint}`;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    // In a real implementation, this would be a proper auth token
                    'Authorization': 'Bearer secure-token-for-development'
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(url, options);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API call to ${endpoint} failed:`, error);
            throw error;
        }
    }
}