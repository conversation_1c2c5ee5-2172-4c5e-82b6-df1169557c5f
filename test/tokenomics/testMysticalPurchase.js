import { UserSimulator } from './UserSimulator.js';

const config = {
  apiBaseUrl: 'http://localhost:3001/api',
  hardhatUrl: 'http://localhost:8545',
  chainId: 31337,
  hotWalletAddress: '******************************************'
};

async function testMysticalEnvironmentPurchase() {
  console.log('🧪 Testing Mystical Environment Purchase...');

  const simulator = new UserSimulator({
    type: 'whale',
    account: {
      address: '******************************************',
      privateKey: '0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6'
    },
    config: config,
    id: 'test_user',
    transactionTracker: null
  });

  try {
    await simulator.initializeWalletBalance();
    await simulator.authenticateWithServer();
    console.log('✅ Simulator initialized');

    const result = await simulator.simulateMysticalEnvironmentPurchases();
    console.log('✅ Mystical environment purchase completed:', result);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testMysticalEnvironmentPurchase();
